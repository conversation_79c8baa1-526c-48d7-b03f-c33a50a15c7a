// Firebase Service Module - Centralized Firebase Operations
class FirebaseService {
    constructor() {
        this.app = null;
        this.auth = null;
        this.db = null;
        this.initialized = false;
        this.initPromise = null;
    }

    // Initialize Firebase
    async initialize() {
        if (this.initPromise) {
            return this.initPromise;
        }

        this.initPromise = this._initializeFirebase();
        return this.initPromise;
    }

    async _initializeFirebase() {
        try {
            // Import Firebase modules
            const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
            const { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, onAuthStateChanged } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            const { getFirestore, collection, doc, addDoc, getDoc, getDocs, updateDoc, deleteDoc, query, where, orderBy } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

            // Firebase configuration
            const firebaseConfig = {
                apiKey: "AIzaSyA0gXeFSshTDc-tAlXYHK_xezkNblz7GMg",
                authDomain: "bitbot-d967b.firebaseapp.com",
                projectId: "bitbot-d967b",
                storageBucket: "bitbot-d967b.firebasestorage.app",
                messagingSenderId: "416388708240",
                appId: "1:416388708240:web:dbbc2c271ca15e36cfe515",
                measurementId: "G-9L48V12SBB"
            };

            // Initialize Firebase
            this.app = initializeApp(firebaseConfig);
            this.auth = getAuth(this.app);
            this.db = getFirestore(this.app);

            // Store Firebase functions for later use
            this.firebaseAuth = {
                signInWithEmailAndPassword,
                createUserWithEmailAndPassword,
                signOut,
                onAuthStateChanged
            };

            this.firestore = {
                collection,
                doc,
                addDoc,
                getDoc,
                getDocs,
                updateDoc,
                deleteDoc,
                query,
                where,
                orderBy
            };

            this.initialized = true;
            console.log('Firebase initialized successfully');
            return true;
        } catch (error) {
            console.error('Firebase initialization failed:', error);
            throw error;
        }
    }

    // Authentication Methods
    async signInAdmin(email, password) {
        await this.initialize();
        try {
            const userCredential = await this.firebaseAuth.signInWithEmailAndPassword(this.auth, email, password);
            
            // Check if user is admin
            const adminDoc = await this.getDoc(this.firestore.doc(this.db, 'admins', userCredential.user.uid));
            if (!adminDoc.exists()) {
                throw new Error('User is not an admin');
            }
            
            return {
                user: userCredential.user,
                adminData: adminDoc.data()
            };
        } catch (error) {
            console.error('Admin sign in failed:', error);
            throw error;
        }
    }

    async createAdmin(email, password, adminData) {
        await this.initialize();
        try {
            const userCredential = await this.firebaseAuth.createUserWithEmailAndPassword(this.auth, email, password);
            
            // Add admin data to Firestore
            await this.firestore.addDoc(this.firestore.collection(this.db, 'admins'), {
                uid: userCredential.user.uid,
                email: email,
                ...adminData,
                createdAt: new Date().toISOString(),
                role: 'admin'
            });
            
            return userCredential.user;
        } catch (error) {
            console.error('Admin creation failed:', error);
            throw error;
        }
    }

    async signOut() {
        await this.initialize();
        return this.firebaseAuth.signOut(this.auth);
    }

    // Student Management Methods
    async addStudent(studentData) {
        await this.initialize();
        try {
            const docRef = await this.firestore.addDoc(this.firestore.collection(this.db, 'students'), {
                ...studentData,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
            
            return { id: docRef.id, ...studentData };
        } catch (error) {
            console.error('Error adding student:', error);
            throw error;
        }
    }

    async getStudents() {
        await this.initialize();
        try {
            const querySnapshot = await this.firestore.getDocs(
                this.firestore.query(
                    this.firestore.collection(this.db, 'students'),
                    this.firestore.orderBy('createdAt', 'desc')
                )
            );
            
            const students = [];
            querySnapshot.forEach((doc) => {
                students.push({ id: doc.id, ...doc.data() });
            });
            
            return students;
        } catch (error) {
            console.error('Error getting students:', error);
            throw error;
        }
    }

    async updateStudent(studentId, updateData) {
        await this.initialize();
        try {
            const studentRef = this.firestore.doc(this.db, 'students', studentId);
            await this.firestore.updateDoc(studentRef, {
                ...updateData,
                updatedAt: new Date().toISOString()
            });
            
            return true;
        } catch (error) {
            console.error('Error updating student:', error);
            throw error;
        }
    }

    async deleteStudent(studentId) {
        await this.initialize();
        try {
            await this.firestore.deleteDoc(this.firestore.doc(this.db, 'students', studentId));
            return true;
        } catch (error) {
            console.error('Error deleting student:', error);
            throw error;
        }
    }

    async getStudentByEmail(email) {
        await this.initialize();
        try {
            const q = this.firestore.query(
                this.firestore.collection(this.db, 'students'),
                this.firestore.where('email', '==', email)
            );
            
            const querySnapshot = await this.firestore.getDocs(q);
            
            if (querySnapshot.empty) {
                return null;
            }
            
            const doc = querySnapshot.docs[0];
            return { id: doc.id, ...doc.data() };
        } catch (error) {
            console.error('Error getting student by email:', error);
            throw error;
        }
    }

    // Admin Panel Content Methods
    async addContent(contentData) {
        await this.initialize();
        try {
            const docRef = await this.firestore.addDoc(this.firestore.collection(this.db, 'content'), {
                ...contentData,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
            
            return { id: docRef.id, ...contentData };
        } catch (error) {
            console.error('Error adding content:', error);
            throw error;
        }
    }

    async getContent(category = null) {
        await this.initialize();
        try {
            let q = this.firestore.query(
                this.firestore.collection(this.db, 'content'),
                this.firestore.orderBy('createdAt', 'desc')
            );
            
            if (category) {
                q = this.firestore.query(
                    this.firestore.collection(this.db, 'content'),
                    this.firestore.where('category', '==', category),
                    this.firestore.orderBy('createdAt', 'desc')
                );
            }
            
            const querySnapshot = await this.firestore.getDocs(q);
            
            const content = [];
            querySnapshot.forEach((doc) => {
                content.push({ id: doc.id, ...doc.data() });
            });
            
            return content;
        } catch (error) {
            console.error('Error getting content:', error);
            throw error;
        }
    }

    async updateContent(contentId, updateData) {
        await this.initialize();
        try {
            const contentRef = this.firestore.doc(this.db, 'content', contentId);
            await this.firestore.updateDoc(contentRef, {
                ...updateData,
                updatedAt: new Date().toISOString()
            });
            
            return true;
        } catch (error) {
            console.error('Error updating content:', error);
            throw error;
        }
    }

    async deleteContent(contentId) {
        await this.initialize();
        try {
            await this.firestore.deleteDoc(this.firestore.doc(this.db, 'content', contentId));
            return true;
        } catch (error) {
            console.error('Error deleting content:', error);
            throw error;
        }
    }

    // Student Personal Data Methods
    async getStudentPersonalData(studentId) {
        await this.initialize();
        try {
            const docRef = this.firestore.doc(this.db, 'student_data', studentId);
            const docSnap = await this.firestore.getDoc(docRef);
            
            if (docSnap.exists()) {
                return { id: docSnap.id, ...docSnap.data() };
            } else {
                return null;
            }
        } catch (error) {
            console.error('Error getting student personal data:', error);
            throw error;
        }
    }

    async updateStudentPersonalData(studentId, personalData) {
        await this.initialize();
        try {
            const docRef = this.firestore.doc(this.db, 'student_data', studentId);
            await this.firestore.updateDoc(docRef, {
                ...personalData,
                updatedAt: new Date().toISOString()
            });
            
            return true;
        } catch (error) {
            console.error('Error updating student personal data:', error);
            throw error;
        }
    }

    // Utility Methods
    onAuthStateChanged(callback) {
        if (this.auth) {
            return this.firebaseAuth.onAuthStateChanged(this.auth, callback);
        }
        return null;
    }

    getCurrentUser() {
        return this.auth ? this.auth.currentUser : null;
    }
}

// Create global instance
window.firebaseService = new FirebaseService();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FirebaseService;
}
