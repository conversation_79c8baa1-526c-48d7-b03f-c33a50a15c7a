<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Setup - BitBot</title>
    <link rel="stylesheet" href="login.css">
    <style>
        .setup-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            margin-right: 10px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .message {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .admin-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }
        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #28a745; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="setup-container">
            <h2>Admin Account Setup</h2>
            
            <!-- Current Admin Status -->
            <div class="section">
                <h3>Current Admin Status</h3>
                <div id="adminStatus" class="admin-info">
                    Checking admin status...
                </div>
            </div>
            
            <!-- Create/Update Admin Section -->
            <div class="section">
                <h3 id="adminFormTitle">Create Admin Account</h3>
                <div id="adminMessage" class="message" style="display: none;"></div>
                
                <form id="adminForm">
                    <div class="form-group">
                        <label class="form-label">Admin Email</label>
                        <input type="email" id="adminEmail" placeholder="<EMAIL>" required>
                        <small style="color: #666;">This will be used to login to the admin panel</small>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Admin Password</label>
                        <input type="password" id="adminPassword" placeholder="Enter secure password" required>
                        <div id="passwordStrength" class="password-strength"></div>
                        <small style="color: #666;">Use a strong password with at least 8 characters</small>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Confirm Password</label>
                        <input type="password" id="confirmPassword" placeholder="Confirm password" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" id="submitBtn">Create Admin Account</button>
                </form>
            </div>
            
            <!-- Admin Management Section -->
            <div class="section" id="managementSection" style="display: none;">
                <h3>Admin Management</h3>
                <div id="managementMessage" class="message" style="display: none;"></div>
                
                <div style="margin-bottom: 15px;">
                    <button class="btn btn-success" onclick="changePassword()">Change Password</button>
                    <button class="btn btn-danger" onclick="resetAdmin()">Reset Admin Account</button>
                </div>
                
                <div class="admin-info">
                    <strong>Security Tips:</strong>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Use a strong, unique password</li>
                        <li>Don't share admin credentials</li>
                        <li>Change password regularly</li>
                        <li>Keep admin email secure</li>
                    </ul>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <a href="admin-login.html" class="btn btn-primary">← Back to Admin Login</a>
            </div>
        </div>
    </div>

    <script src="firebase-service.js"></script>
    <script>
        let currentAdmin = null;
        let firebaseService = window.firebaseService;

        document.addEventListener('DOMContentLoaded', async function() {
            await checkAdminStatus();

            // Form submission handler
            document.getElementById('adminForm').addEventListener('submit', function(e) {
                e.preventDefault();
                handleAdminSetup();
            });

            // Password strength checker
            document.getElementById('adminPassword').addEventListener('input', checkPasswordStrength);
        });

        async function checkAdminStatus() {
            const statusDiv = document.getElementById('adminStatus');
            const formTitle = document.getElementById('adminFormTitle');
            const submitBtn = document.getElementById('submitBtn');
            const managementSection = document.getElementById('managementSection');

            try {
                await firebaseService.initialize();
                const currentUser = firebaseService.getCurrentUser();

                if (currentUser) {
                    // Check if current user is admin
                    try {
                        const adminData = await firebaseService.getAdminByUid(currentUser.uid);
                        if (adminData) {
                            currentAdmin = { ...adminData, uid: currentUser.uid, email: currentUser.email };
                            statusDiv.innerHTML = `
                                <strong>✅ Admin Account Active (Firebase)</strong><br>
                                Email: ${currentUser.email}<br>
                                UID: ${currentUser.uid}<br>
                                Created: ${new Date(adminData.createdAt).toLocaleString()}
                            `;
                            statusDiv.className = 'admin-info';

                            formTitle.textContent = 'Admin Account Active';
                            submitBtn.textContent = 'Create Another Admin';
                            managementSection.style.display = 'block';

                            return;
                        }
                    } catch (error) {
                        console.warn('Error checking admin status:', error);
                    }
                }

                // No admin found
                statusDiv.innerHTML = '<strong>🔧 No admin account found</strong><br>Create your first admin account below';
                statusDiv.className = 'admin-info';
                formTitle.textContent = 'Create Admin Account';
                submitBtn.textContent = 'Create Admin Account';
                managementSection.style.display = 'none';

            } catch (error) {
                console.error('Error checking admin status:', error);
                statusDiv.innerHTML = '<strong>⚠️ Error connecting to Firebase</strong><br>Please check your internet connection';
                statusDiv.className = 'admin-info';
            }
        }

        async function handleAdminSetup() {
            const email = document.getElementById('adminEmail').value.trim();
            const password = document.getElementById('adminPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // Validation
            if (!email || !password || !confirmPassword) {
                showMessage('adminMessage', 'Please fill in all fields', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('adminMessage', 'Passwords do not match', 'error');
                return;
            }

            if (password.length < 8) {
                showMessage('adminMessage', 'Password must be at least 8 characters long', 'error');
                return;
            }

            try {
                showMessage('adminMessage', 'Creating admin account...', 'info');

                // Create admin using Firebase Authentication
                const adminData = {
                    name: 'Admin User',
                    role: 'admin',
                    createdAt: new Date().toISOString()
                };

                const result = await firebaseService.createAdmin(email, password, adminData);

                showMessage('adminMessage', 'Admin account created successfully in Firebase Authentication! You can now login.', 'success');

                // Reset form
                document.getElementById('adminForm').reset();

                // Add login button
                setTimeout(() => {
                    const messageDiv = document.getElementById('adminMessage');
                    messageDiv.innerHTML += '<br><a href="admin-login.html" class="btn btn-primary" style="margin-top: 10px; text-decoration: none; display: inline-block; padding: 8px 16px; background: #007bff; color: white; border-radius: 4px;">Go to Admin Login</a>';
                }, 1000);

            } catch (error) {
                console.error('Error creating admin:', error);

                if (error.code === 'auth/email-already-in-use') {
                    showMessage('adminMessage', 'Email already in use. Please use a different email or login with existing account.', 'error');
                    // Add login button for existing users
                    setTimeout(() => {
                        const messageDiv = document.getElementById('adminMessage');
                        messageDiv.innerHTML += '<br><a href="admin-login.html" class="btn btn-primary" style="margin-top: 10px; text-decoration: none; display: inline-block; padding: 8px 16px; background: #007bff; color: white; border-radius: 4px;">Go to Admin Login</a>';
                    }, 1000);
                } else if (error.code === 'auth/weak-password') {
                    showMessage('adminMessage', 'Password is too weak. Please use a stronger password.', 'error');
                } else if (error.code === 'auth/invalid-email') {
                    showMessage('adminMessage', 'Invalid email address format.', 'error');
                } else {
                    showMessage('adminMessage', `Failed to create admin: ${error.message}`, 'error');
                }
            }
        }

        function checkPasswordStrength() {
            const password = document.getElementById('adminPassword').value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthDiv.textContent = '';
                return;
            }
            
            let strength = 0;
            let feedback = [];
            
            // Length check
            if (password.length >= 8) strength++;
            else feedback.push('at least 8 characters');
            
            // Uppercase check
            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('uppercase letter');
            
            // Lowercase check
            if (/[a-z]/.test(password)) strength++;
            else feedback.push('lowercase letter');
            
            // Number check
            if (/\d/.test(password)) strength++;
            else feedback.push('number');
            
            // Special character check
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
            else feedback.push('special character');
            
            let strengthText = '';
            let strengthClass = '';
            
            if (strength < 2) {
                strengthText = 'Weak';
                strengthClass = 'strength-weak';
            } else if (strength < 4) {
                strengthText = 'Medium';
                strengthClass = 'strength-medium';
            } else {
                strengthText = 'Strong';
                strengthClass = 'strength-strong';
            }
            
            strengthDiv.innerHTML = `Password strength: <span class="${strengthClass}">${strengthText}</span>`;
            
            if (feedback.length > 0 && strength < 4) {
                strengthDiv.innerHTML += `<br><small>Add: ${feedback.join(', ')}</small>`;
            }
        }

        async function changePassword() {
            const currentUser = firebaseService.getCurrentUser();
            if (!currentUser) {
                showMessage('managementMessage', 'You must be logged in to change password', 'error');
                return;
            }

            const newPassword = prompt('Enter new admin password:');
            if (!newPassword) return;

            if (newPassword.length < 8) {
                showMessage('managementMessage', 'Password must be at least 8 characters long', 'error');
                return;
            }

            const confirmPassword = prompt('Confirm new password:');
            if (newPassword !== confirmPassword) {
                showMessage('managementMessage', 'Passwords do not match', 'error');
                return;
            }

            try {
                // Update password in Firebase Auth
                await firebaseService.updatePassword(newPassword);
                showMessage('managementMessage', 'Password changed successfully in Firebase!', 'success');
            } catch (error) {
                console.error('Error changing password:', error);
                showMessage('managementMessage', `Failed to change password: ${error.message}`, 'error');
            }
        }

        async function resetAdmin() {
            if (confirm('Are you sure you want to sign out? You will need to login again.')) {
                try {
                    await firebaseService.signOut();
                    showMessage('managementMessage', 'Signed out successfully!', 'success');

                    setTimeout(() => {
                        window.location.href = 'admin-login.html';
                    }, 1000);
                } catch (error) {
                    console.error('Error signing out:', error);
                    showMessage('managementMessage', `Failed to sign out: ${error.message}`, 'error');
                }
            }
        }

        function showMessage(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `message ${type}`;
            element.style.display = 'block';
            
            setTimeout(() => {
                element.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
